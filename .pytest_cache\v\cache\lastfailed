{"test_stats_fix.py::test_stats_display": true, "tests/ui/test_main_window.py::test_main_window_initialization": true, "tests/ui/test_main_window.py::test_file_tree_update": true, "tests/ui/test_factory.py::TestUIFactory::test_create_rename_panel": true, "tests/ui/test_ui_controls.py::TestUIControls::test_main_window_controls": true, "tests/ui/test_ui_controls.py::TestUIControls::test_function_panels": true, "tests/ui/test_ui_controls.py::TestUIControls::test_status_bar": true, "tests/ui/test_ui_controls.py::TestUIControls::test_notebook_tabs": true, "tests/ui/test_ui_controls.py::TestUIControls::test_theme_application": true, "tests/integration/test_mongodb_integration.py::test_progress_callback": true, "tests/integration/test_mongodb_integration.py::test_event_handler": true, "tests/integration/test_mongodb_integration.py::MongoDBUITest::test_ui_containment": true, "tests/test_rule_engine.py::TestRuleEngine::test_apply_pattern": true, "tests/test_rule_engine.py::TestRuleEngine::test_rename_file": true, "tests/test_rule_engine.py::TestRuleEngine::test_rule_persistence": true, "tests/test_rule_engine.py::TestRuleEngine::test_rule_validation": true, "tests/performance/test_optimization_benchmark.py": true, "src/tests/test_duplicate_finder.py::TestLocalDuplicateFinder::test_interruption": true, "src/tests/test_duplicate_finder.py::PerformanceTestDuplicateFinder::test_performance": true, "src/tests/test_debounce_config_manager.py::test_load_config": true, "src/tests/test_debounce_config_manager.py::test_save_config": true, "src/tests/test_debounce_config_manager.py::test_feature_enabled_control": true, "src/tests/test_debounce_config_manager.py::test_feature_names": true, "src/tests/test_debounce_config_manager.py::test_error_handling": true, "src/tests/performance/test_debounce_performance.py": true, "src/tests/test_file_tree_debounce.py": true, "src/tests/test_debounce_settings_panel.py::test_global_switch": true, "src/tests/test_debounce_settings_panel.py::test_enable_all": true, "src/tests/test_debounce_settings_panel.py::test_ui_layout": true, "reusable_widgets/test_widgets.py::test_vertical_progress_bar": true, "src/tests/test_file_tree_e2e.py::test_file_tree_panel_e2e": true, "src/tests/test_file_tree_ui.py::test_file_tree_panel_lazy_load": true, "src/tests/test_progress_callback_debounce.py::test_initial_state": true, "src/tests/test_progress_callback_debounce.py::test_callback_without_debounce": true, "src/tests/test_progress_callback_debounce.py::test_callback_with_debounce": true, "src/tests/test_progress_callback_debounce.py::test_dynamic_window_calculation": true, "src/tests/test_progress_callback_debounce.py::test_force_update": true, "src/tests/test_progress_callback_debounce.py::test_error_handling": true, "src/tests/test_progress_callback_debounce.py::test_callback_error_handling": true, "src/tests/test_progress_callback_debounce.py::test_window_display_update": true, "src/tests/test_main_window_integration.py::test_initial_state": true, "src/tests/test_main_window_integration.py::test_menu_items": true, "src/tests/test_main_window_integration.py::test_toolbar_buttons": true, "src/tests/test_main_window_integration.py::test_show_debounce_settings": true, "src/tests/test_main_window_integration.py::test_settings_window_position": true, "src/tests/test_main_window_integration.py::test_settings_window_close": true, "src/tests/test_main_window_integration.py::test_multiple_open_attempts": true, "src/tests/test_main_window_integration.py::test_ui_interaction": true, "src/tests/ui/test_debounce_ui.py::TestDebounceSettingsPanelUI::test_layout": true, "src/tests/ui/test_debounce_ui.py::TestDebounceSettingsPanelUI::test_global_switch": true, "src/tests/ui/test_debounce_ui.py::TestDebounceSettingsPanelUI::test_feature_switches": true, "src/tests/ui/test_debounce_ui.py::TestDebounceSettingsPanelUI::test_all_buttons": true, "src/tests/ui/test_debounce_ui.py::TestDebounceSettingsPanelUI::test_window_display": true, "src/tests/ui/test_debounce_ui.py::TestSettingsPanelIntegration::test_debounce_panel_integration": true, "src/tests/ui/test_debounce_ui.py::TestSettingsPanelIntegration::test_window_display_update": true, "src/tests/ui/test_debounce_ui.py::TestMainWindowIntegration::test_menu_integration": true, "src/tests/ui/test_debounce_ui.py::TestMainWindowIntegration::test_toolbar_integration": true, "src/tests/ui/test_debounce_ui.py::TestMainWindowIntegration::test_settings_window": true, "src/tests/ui/test_debounce_ui.py::TestMainWindowIntegration::test_multiple_window_open": true, "src/tests/test_dynamic_window_calculator.py::test_items_count_window": true, "src/tests/test_dynamic_window_calculator.py::test_file_size_window": true, "simple_db_test.py::test_simple_db_insert": true, "src/tests/test_async_manager.py::test_submit_and_cancel_task": true, "src/tests/test_async_manager.py::test_submit_coroutine": true, "src/tests/test_async_stress.py::test_file_operations_stress": true, "src/tests/test_async_stress.py::test_video_analyzer_stress": true, "src/tests/test_file_tree_compatibility.py::test_fileinfo_fields_compatibility": true, "src/tests/test_file_tree_edge_cases.py::test_fileinfo_special_paths": true, "src/tests/test_file_tree_edge_cases.py::test_fileinfo_deep_nesting": true, "src/tests/test_file_tree_edge_cases.py::test_fileinfo_empty_and_large": true, "src/tests/test_file_tree_index_service.py::test_build_and_query_index": true, "src/tests/test_file_tree_index_service.py::test_update_index": true, "src/tests/test_file_tree_index_service.py::test_cache": true, "src/tests/test_file_tree_integration.py::test_integration_scan_and_lazy_load": true, "src/tests/test_file_tree_performance.py::test_file_tree_index_performance": true, "src/tests/test_settings_panel_integration.py::test_initial_state": true, "src/tests/test_settings_panel_integration.py::test_tab_names": true, "src/tests/test_settings_panel_integration.py::test_window_display_update": true, "src/tests/test_settings_panel_integration.py::test_show_debounce_settings": true, "src/tests/test_settings_panel_integration.py::test_debounce_panel_layout": true, "src/tests/test_settings_panel_integration.py::test_config_manager_integration": true, "src/tests/test_settings_panel_integration.py::test_ui_visibility": true, "src/tests/test_settings_panel_integration.py::test_tab_switching": true, "src/tests/test_settings_panel_integration.py::test_error_handling": true, "src/tests/test_whitelist_panel_debounce.py::test_initial_state": true, "src/tests/test_whitelist_panel_debounce.py::test_update_without_debounce": true, "src/tests/test_whitelist_panel_debounce.py::test_update_with_debounce": true, "src/tests/test_whitelist_panel_debounce.py::test_dynamic_window_calculation": true, "src/tests/test_whitelist_panel_debounce.py::test_force_refresh": true, "src/tests/test_whitelist_panel_debounce.py::test_error_handling": true, "src/tests/test_whitelist_panel_debounce.py::test_window_display_update": true, "test_file_tree_hierarchy.py::test_file_tree_hierarchy": true, "test_file_tree_hierarchy.py::test_path_normalization": true, "test_fixes_verification.py::test_config_loader_fix": true, "test_fixes_verification.py::test_enhanced_task_overview_panel": true, "test_fixes_verification.py::test_task_lifecycle": true, "test_interrupt_hash_calculation.py::test_hash_calculation_interrupt": true, "test_interrupt_hash_calculation.py::test_with_existing_calculation": true, "test_layout_improvements.py::test_resizable_paned_window": true, "test_layout_improvements.py::test_task_overview_panel": true, "test_layout_improvements.py::test_combined_layout": true, "src/tests/test_file_operations.py::TestFileOperationsAsync::test_async_batch_delete": true, "src/tests/test_file_operations.py::TestFileOperationsAsync::test_async_batch_rename": true, "src/tests/test_file_operations.py::TestFileOperationsAsync::test_async_batch_with_exception": true, "src/tests/test_file_operations.py::TestFileOperationsAsync::test_cross_platform_path": true, "src/tests/test_file_operations.py::TestFileOperationsAsync::test_empty_and_illegal_path": true, "src/tests/test_file_operations.py::TestFileOperationsAsync::test_path_normalization_and_compatibility": true, "src/tests/test_video_analyzer.py::TestVideoAnalyzerAsync::test_async_batch_analyze": true, "src/tests/test_video_analyzer.py::TestVideoAnalyzerAsync::test_async_batch_with_exception": true, "docs/testing/test_multi_directory_scan.py::test_multi_directory_scan": true, "src/tests/test_async_scan_integration.py::test_full_scan_pipeline": true, "test_enhanced_interrupt.py::test_enhanced_interrupt_system": true, "test_enhanced_interrupt.py::test_file_scanner_with_enhanced_interrupt": true, "test_enhanced_interrupt.py::test_performance_optimization": true, "test_file_id.py::test_file_info_creation": true, "test_file_id.py::test_database_operations": true, "test_file_id.py::test_file_scanner": true, "test_file_id.py::test_file_tree_integration": true, "test_scan_timeout_fix.py::test_scan_timeout_fix": true, "docs/testing/test_multi_directory_scan.py": true, "final_test.py": true, "test_enhanced_interrupt.py": true, "test_file_id.py": true, "test_file_scan_db_integration.py": true, "test_file_tree_hierarchy.py": true, "test_interrupt_hash_calculation.py": true, "test_scan_timeout_fix.py": true, "src/tests/test_dynamic_window_calculator.py::test_basic_window": true, "src/tests/test_dynamic_window_calculator.py::test_config_update": true, "src/tests/test_dynamic_window_calculator.py::test_custom_config": true, "src/tests/test_dynamic_window_calculator.py::test_window_bounds": true, "src/tests/test_dynamic_window_calculator.py::test_error_handling": true, "src/tests/test_dynamic_window_calculator.py::test_mixed_data_types": true, "src/tests/test_refresh_dedup_guard.py::test_basic_refresh": true, "src/tests/test_refresh_dedup_guard.py::test_dynamic_window": true, "src/tests/test_refresh_dedup_guard.py::test_force_refresh": true, "src/tests/test_refresh_dedup_guard.py::test_multiple_features": true, "src/tests/test_refresh_dedup_guard.py::test_complex_data_types": true, "src/tests/test_refresh_dedup_guard.py::test_reset": true, "src/tests/test_refresh_dedup_guard.py::test_invalid_dynamic_window": true, "src/tests/test_refresh_dedup_guard.py::test_non_serializable_data": true, "test_final_layout.py::test_final_layout": true, "test_interrupt_functionality.py::test_interrupt_functionality": true, "test_interrupt_functionality.py::test_unified_task_manager": true, "test_log_buffer.py::test_log_buffer_basic": true, "test_log_buffer.py::test_log_buffer_frequency_limit": true, "test_log_buffer.py::test_log_buffer_task_completion": true, "test_log_buffer.py::test_log_buffer_concurrent": true, "test_logging_system.py::test_basic_logging": true, "test_logging_system.py::test_ui_log_callback": true, "test_logging_system.py::test_module_logging": true, "test_logging_system.py::test_concurrent_logging": true, "test_place_layout.py::test_place_layout": true, "test_progress_system.py::test_progress_manager": true, "test_progress_system.py::test_progress_callback_creation": true, "test_progress_system.py::test_task_status_transitions": true, "test_status_bar_fixed.py::test_status_bar_fixed": true, "test_status_bar_layout.py::test_status_bar": true, "src/tests/performance/test_optimization_benchmark.py::test_hash_calculator_performance": true, "src/tests/performance/test_optimization_benchmark.py::test_batch_processor_performance": true, "src/tests/performance/test_optimization_benchmark.py::test_duplicate_finder_performance": true, "src/tests/comprehensive/test_folder_selection_and_scanning.py::TestFolderSelectionAndScanning::test_basic_directory_scanning": true, "src/tests/test_duplicate_finder.py::TestLocalDuplicateFinder::test_find_duplicates": true, "src/tests/test_duplicate_finder.py::TestDuplicateFinder::test_find_duplicates": true, "src/tests/test_optimized_task_panel.py": true, "src/tests/performance/test_performance_benchmarks.py": true, "src/tests/test_integration.py::TestSystemStability::test_rename_service_integration": true, "src/tests/test_integration.py::TestSystemStability::test_file_operations_service_integration": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_hash_calculation": true, "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_request_validation": true, "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_result_operations": true, "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rule_operations": true, "src/tests/test_file_operations_service.py::TestFileOperationsServiceImpl::test_conflict_resolution_rename": true, "src/tests/test_file_operations_service.py::TestFileOperationsServiceImpl::test_conflict_resolution_skip": true, "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rule_template_operations_extended": true, "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_service_configuration_and_status": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_algorithm_core": true, "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rule_template_save_and_load": true, "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rule_template_validation_and_error_handling": true, "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_get_rule_templates_functionality": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_service_configuration": true, "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_conflict_resolution_comprehensive": true, "src/tests/test_file_scan_service.py::TestFileScanServiceImpl::test_concurrent_scans": true, "src/tests/test_file_scan_service.py::TestFileScanServiceImpl::test_pause_resume_scan": true, "src/tests/test_file_scan_service.py::TestFileScanServiceImpl::test_scan_cancellation": true, "src/tests/test_file_scan_service.py::TestFileScanServiceImpl::test_scan_directories": true, "src/tests/test_file_scan_service.py::TestFileScanServiceImpl::test_scan_priority_setting": true, "src/tests/test_file_scan_service.py::TestFileScanServiceImpl::test_scan_with_invalid_directory": true, "src/tests/test_file_scan_service.py::TestFileScanServiceImpl::test_task_management": true, "src/tests/test_file_scan_service.py::TestFileScanServiceIntegration::test_concurrent_scans": true, "src/tests/test_file_scan_service.py::TestFileScanServiceIntegration::test_directory_estimation": true, "src/tests/test_file_scan_service.py::TestFileScanServiceIntegration::test_edge_cases": true, "src/tests/test_file_scan_service.py::TestFileScanServiceIntegration::test_error_handling": true, "src/tests/test_file_scan_service.py::TestFileScanServiceIntegration::test_event_publishing": true, "src/tests/test_file_scan_service.py::TestFileScanServiceIntegration::test_file_processing_operations": true, "src/tests/test_file_scan_service.py::TestFileScanServiceIntegration::test_priority_management": true, "src/tests/test_file_scan_service.py::TestFileScanServiceIntegration::test_scan_cancellation": true, "src/tests/test_file_scan_service.py::TestFileScanServiceIntegration::test_service_lifecycle": true, "src/tests/test_file_scan_service.py::TestFileScanServiceIntegration::test_task_management_operations": true, "src/tests/test_file_scan_service.py::TestFileScanServiceIntegration::test_validation_operations": true, "src/tests/test_integration_services.py::TestServicesIntegration": true, "src/tests/test_duplicate_detection_service.py::TestDuplicateDetectionServiceImpl::test_detection_cancellation": true, "src/tests/test_duplicate_detection_service.py::TestDuplicateDetectionServiceImpl::test_duplicate_detection_with_mock": true, "src/tests/test_duplicate_detection_service.py::TestDuplicateDetectionServiceImpl::test_service_lifecycle": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_actual_detection_execution_flow": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_concurrent_detection": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_algorithms": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_cancellation": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_cancellation_and_cleanup": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_core_methods": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_edge_cases_extended": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_error_handling_comprehensive": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_monitoring_and_progress": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_progress_monitoring": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_request_validation": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_result_comprehensive": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_result_operations": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_service_methods_coverage": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_task_lifecycle": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_task_management_extended": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_validation_comprehensive": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_validation_methods": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_duplicate_group_operations_extended": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_duplicate_groups_management": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_edge_cases_and_error_handling": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_error_handling_scenarios": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_file_collection_operations": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_service_configuration_and_limits": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_service_health_and_status": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_simple_detection_flow": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_space_calculation": true, "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_space_calculation_extended": true, "src/tests/test_file_scan_service_extended.py::TestFileScanServiceExtended::test_directory_estimation": true, "src/tests/test_file_scan_service_extended.py::TestFileScanServiceExtended::test_error_handling": true, "src/tests/test_file_scan_service_extended.py::TestFileScanServiceExtended::test_file_collection": true, "src/tests/test_file_scan_service_extended.py::TestFileScanServiceExtended::test_file_processing_operations": true, "src/tests/test_file_scan_service_extended.py::TestFileScanServiceExtended::test_priority_management": true, "src/tests/test_file_scan_service_extended.py::TestFileScanServiceExtended::test_scan_result_operations": true, "src/tests/test_file_scan_service_extended.py::TestFileScanServiceExtended::test_task_management_operations": true, "src/tests/test_file_scan_service_extended.py::TestFileScanServiceExtended::test_validation_operations": true}