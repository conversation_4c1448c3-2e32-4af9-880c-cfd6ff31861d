["basic_test.py::test_data_structures", "basic_test.py::test_file_node", "basic_test.py::test_imports", "basic_test.py::test_memory_manager_creation", "docs/testing/test_multi_directory_scan.py::test_multi_directory_scan", "reusable_widgets/test_widgets.py::test_all_widgets", "reusable_widgets/test_widgets.py::test_progress_indicator", "reusable_widgets/test_widgets.py::test_status_light", "reusable_widgets/test_widgets.py::test_status_panel", "reusable_widgets/test_widgets.py::test_system_monitor", "reusable_widgets/test_widgets.py::test_vertical_progress_bar", "simple_db_test.py::test_simple_db_insert", "simple_memory_test.py::test_file_node", "simple_memory_test.py::test_memory_file_tree", "src/tests/comprehensive/test_app_startup_and_db_init.py::TestAppStartupAndDatabaseInit::test_concurrent_database_operations", "src/tests/comprehensive/test_app_startup_and_db_init.py::TestAppStartupAndDatabaseInit::test_database_connection_failure", "src/tests/comprehensive/test_app_startup_and_db_init.py::TestAppStartupAndDatabaseInit::test_database_connection_success", "src/tests/comprehensive/test_app_startup_and_db_init.py::TestAppStartupAndDatabaseInit::test_database_retry_mechanism", "src/tests/comprehensive/test_app_startup_and_db_init.py::TestAppStartupAndDatabaseInit::test_database_schema_validation", "src/tests/comprehensive/test_app_startup_and_db_init.py::TestAppStartupAndDatabaseInit::test_empty_database_handling", "src/tests/comprehensive/test_app_startup_and_db_init.py::TestAppStartupAndDatabaseInit::test_file_tree_generation_from_existing_data", "src/tests/comprehensive/test_app_startup_and_db_init.py::TestAppStartupAndDatabaseInit::test_main_window_initialization_with_valid_db", "src/tests/comprehensive/test_duplicate_detection.py::TestDuplicateDetection::test_duplicate_deletion_workflow", "src/tests/comprehensive/test_duplicate_detection.py::TestDuplicateDetection::test_file_tree_refresh_after_duplicate_removal", "src/tests/comprehensive/test_duplicate_detection.py::TestDuplicateDetection::test_hash_based_duplicate_detection", "src/tests/comprehensive/test_duplicate_detection.py::TestDuplicateDetection::test_multi_level_hash_detection", "src/tests/comprehensive/test_duplicate_detection.py::TestDuplicateDetection::test_same_content_different_names", "src/tests/comprehensive/test_duplicate_detection.py::TestDuplicateDetection::test_size_based_duplicate_detection", "src/tests/comprehensive/test_duplicate_detection.py::TestDuplicateDetection::test_user_selection_simulation", "src/tests/comprehensive/test_file_operations.py::TestFileOperations::test_backup_functionality", "src/tests/comprehensive/test_file_operations.py::TestFileOperations::test_concurrent_file_operations", "src/tests/comprehensive/test_file_operations.py::TestFileOperations::test_file_deletion_with_database_sync", "src/tests/comprehensive/test_file_operations.py::TestFileOperations::test_file_move_between_directories", "src/tests/comprehensive/test_file_operations.py::TestFileOperations::test_file_operation_error_handling", "src/tests/comprehensive/test_file_operations.py::TestFileOperations::test_file_rename_with_metadata_update", "src/tests/comprehensive/test_file_operations.py::TestFileOperations::test_file_tree_refresh_after_operations", "src/tests/comprehensive/test_file_operations.py::TestFileOperations::test_large_file_operations", "src/tests/comprehensive/test_file_operations.py::TestFileOperations::test_operation_metrics", "src/tests/comprehensive/test_file_operations.py::TestFileOperations::test_operation_rollback", "src/tests/comprehensive/test_file_tree_clear.py::TestFileTreeClear::test_clear_cancellation", "src/tests/comprehensive/test_file_tree_clear.py::TestFileTreeClear::test_clear_confirmation_dialog", "src/tests/comprehensive/test_file_tree_clear.py::TestFileTreeClear::test_clear_operation_error_handling", "src/tests/comprehensive/test_file_tree_clear.py::TestFileTreeClear::test_clear_performance", "src/tests/comprehensive/test_file_tree_clear.py::TestFileTreeClear::test_clear_with_concurrent_operations", "src/tests/comprehensive/test_file_tree_clear.py::TestFileTreeClear::test_clear_with_file_tree_refresh", "src/tests/comprehensive/test_file_tree_clear.py::TestFileTreeClear::test_database_clear_operation", "src/tests/comprehensive/test_file_tree_clear.py::TestFileTreeClear::test_partial_database_clear", "src/tests/comprehensive/test_file_tree_clear.py::TestFileTreeClear::test_ui_state_after_clear", "src/tests/comprehensive/test_folder_selection_and_scanning.py::TestFolderSelectionAndScanning::test_basic_directory_scanning", "src/tests/comprehensive/test_folder_selection_and_scanning.py::TestFolderSelectionAndScanning::test_concurrent_scanning", "src/tests/comprehensive/test_folder_selection_and_scanning.py::TestFolderSelectionAndScanning::test_empty_directory_handling", "src/tests/comprehensive/test_folder_selection_and_scanning.py::TestFolderSelectionAndScanning::test_file_tree_refresh_after_scan", "src/tests/comprehensive/test_folder_selection_and_scanning.py::TestFolderSelectionAndScanning::test_large_directory_performance", "src/tests/comprehensive/test_folder_selection_and_scanning.py::TestFolderSelectionAndScanning::test_metadata_processing", "src/tests/comprehensive/test_folder_selection_and_scanning.py::TestFolderSelectionAndScanning::test_permission_error_handling", "src/tests/comprehensive/test_folder_selection_and_scanning.py::TestFolderSelectionAndScanning::test_progress_callback_functionality", "src/tests/comprehensive/test_folder_selection_and_scanning.py::TestFolderSelectionAndScanning::test_recursive_scanning_depth", "src/tests/comprehensive/test_folder_selection_and_scanning.py::TestFolderSelectionAndScanning::test_scan_interruption", "src/tests/comprehensive/test_whitelist_and_tags.py::TestWhitelistAndTags::test_batch_whitelist_operations", "src/tests/comprehensive/test_whitelist_and_tags.py::TestWhitelistAndTags::test_folder_based_whitelist_detection", "src/tests/comprehensive/test_whitelist_and_tags.py::TestWhitelistAndTags::test_pattern_based_whitelist_detection", "src/tests/comprehensive/test_whitelist_and_tags.py::TestWhitelistAndTags::test_single_file_whitelist_tagging", "src/tests/comprehensive/test_whitelist_and_tags.py::TestWhitelistAndTags::test_whitelist_config_reload", "src/tests/comprehensive/test_whitelist_and_tags.py::TestWhitelistAndTags::test_whitelist_event_system_integration", "src/tests/comprehensive/test_whitelist_and_tags.py::TestWhitelistAndTags::test_whitelist_persistence", "src/tests/comprehensive/test_whitelist_and_tags.py::TestWhitelistAndTags::test_whitelist_statistics", "src/tests/comprehensive/test_whitelist_and_tags.py::TestWhitelistAndTags::test_whitelist_status_filtering", "src/tests/comprehensive/test_whitelist_and_tags.py::TestWhitelistAndTags::test_whitelist_tag_removal_and_modification", "src/tests/performance/test_optimization_benchmark.py::TestPerformanceOptimization::test_batch_processor_performance", "src/tests/performance/test_optimization_benchmark.py::TestPerformanceOptimization::test_duplicate_finder_performance", "src/tests/performance/test_optimization_benchmark.py::TestPerformanceOptimization::test_hash_calculator_performance", "src/tests/performance/test_optimization_benchmark.py::test_batch_processor_performance", "src/tests/performance/test_optimization_benchmark.py::test_duplicate_finder_performance", "src/tests/performance/test_optimization_benchmark.py::test_hash_calculator_performance", "src/tests/performance/test_performance_benchmarks.py::TestPerformanceBenchmarks::test_file_scan_performance", "src/tests/test_adapter_integration.py::TestAdapterPerformance::test_adapter_memory_usage", "src/tests/test_adapter_integration.py::TestAdapterPerformance::test_adapter_response_time", "src/tests/test_adapter_integration.py::TestDuplicateServiceAdapterIntegration::test_adapter_lifecycle", "src/tests/test_adapter_integration.py::TestDuplicateServiceAdapterIntegration::test_async_duplicate_detection", "src/tests/test_adapter_integration.py::TestDuplicateServiceAdapterIntegration::test_concurrent_adapter_operations", "src/tests/test_adapter_integration.py::TestDuplicateServiceAdapterIntegration::test_error_handling_in_adapter", "src/tests/test_adapter_integration.py::TestDuplicateServiceAdapterIntegration::test_global_adapter_access", "src/tests/test_adapter_integration.py::TestDuplicateServiceAdapterIntegration::test_intelligent_duplicate_finding", "src/tests/test_adapter_integration.py::TestDuplicateServiceAdapterIntegration::test_legacy_adapter_compatibility", "src/tests/test_adapter_integration.py::TestDuplicateServiceAdapterIntegration::test_sync_duplicate_detection", "src/tests/test_adapter_integration.py::TestDuplicateServiceAdapterIntegration::test_task_cancellation_through_adapter", "src/tests/test_async_manager.py::TestAsyncManager::test_submit_and_cancel_task", "src/tests/test_async_manager.py::TestAsyncManager::test_submit_and_complete_task", "src/tests/test_async_manager.py::TestAsyncManager::test_submit_coroutine", "src/tests/test_async_manager.py::TestAsyncTaskManager::test_interrupt_async_task", "src/tests/test_async_manager.py::TestAsyncTaskManager::test_submit_and_complete_async_task", "src/tests/test_async_manager.py::test_interrupt_async_task", "src/tests/test_async_manager.py::test_submit_and_cancel_task", "src/tests/test_async_manager.py::test_submit_and_complete_async_task", "src/tests/test_async_manager.py::test_submit_and_complete_task", "src/tests/test_async_manager.py::test_submit_coroutine", "src/tests/test_async_scan_integration.py::test_checkpoint_resume", "src/tests/test_async_scan_integration.py::test_full_scan_pipeline", "src/tests/test_async_stress.py::AsyncStressTest::test_file_operations_stress", "src/tests/test_async_stress.py::AsyncStressTest::test_video_analyzer_stress", "src/tests/test_async_stress.py::test_file_operations_stress", "src/tests/test_async_stress.py::test_video_analyzer_stress", "src/tests/test_debounce_config_manager.py::test_debounce_config_manager_init", "src/tests/test_debounce_config_manager.py::test_error_handling", "src/tests/test_debounce_config_manager.py::test_feature_enabled_control", "src/tests/test_debounce_config_manager.py::test_feature_names", "src/tests/test_debounce_config_manager.py::test_invalid_config_updates", "src/tests/test_debounce_config_manager.py::test_load_config", "src/tests/test_debounce_config_manager.py::test_save_config", "src/tests/test_debounce_config_manager.py::test_window_config_management", "src/tests/test_debounce_settings_panel.py::test_disable_all", "src/tests/test_debounce_settings_panel.py::test_display_names", "src/tests/test_debounce_settings_panel.py::test_enable_all", "src/tests/test_debounce_settings_panel.py::test_feature_switch", "src/tests/test_debounce_settings_panel.py::test_global_switch", "src/tests/test_debounce_settings_panel.py::test_init", "src/tests/test_debounce_settings_panel.py::test_ui_layout", "src/tests/test_debounce_settings_panel.py::test_window_display", "src/tests/test_dto_system.py::TestBaseDTOSystem::test_dto_serialization", "src/tests/test_dto_system.py::TestBaseDTOSystem::test_dto_serialization_errors", "src/tests/test_dto_system.py::TestBaseDTOSystem::test_error_info_creation", "src/tests/test_dto_system.py::TestBaseDTOSystem::test_error_info_edge_cases", "src/tests/test_dto_system.py::TestBaseDTOSystem::test_error_info_validation", "src/tests/test_dto_system.py::TestBaseDTOSystem::test_progress_update_creation", "src/tests/test_dto_system.py::TestBaseDTOSystem::test_progress_update_validation", "src/tests/test_dto_system.py::TestDuplicateDTOSystem::test_duplicate_check_factory", "src/tests/test_dto_system.py::TestDuplicateDTOSystem::test_duplicate_check_request", "src/tests/test_dto_system.py::TestDuplicateDTOSystem::test_duplicate_group_properties", "src/tests/test_dto_system.py::TestDuplicateDTOSystem::test_duplicate_group_validation", "src/tests/test_dto_system.py::TestRenameDTOSystem::test_rename_request_factory", "src/tests/test_dto_system.py::TestRenameDTOSystem::test_rename_request_validation", "src/tests/test_dto_system.py::TestRenameDTOSystem::test_rename_rule_creation", "src/tests/test_dto_system.py::TestRenameDTOSystem::test_rename_rule_validation", "src/tests/test_dto_system.py::TestScanDTOSystem::test_file_info_creation", "src/tests/test_dto_system.py::TestScanDTOSystem::test_scan_request_creation", "src/tests/test_dto_system.py::TestScanDTOSystem::test_scan_request_factory_methods", "src/tests/test_dto_system.py::TestScanDTOSystem::test_scan_request_validation", "src/tests/test_dto_system.py::TestScanDTOSystem::test_scan_result_properties", "src/tests/test_duplicate_detection_service.py::TestDuplicateDetectionServiceImpl::test_detection_cancellation", "src/tests/test_duplicate_detection_service.py::TestDuplicateDetectionServiceImpl::test_detection_request_validation", "src/tests/test_duplicate_detection_service.py::TestDuplicateDetectionServiceImpl::test_detection_statistics", "src/tests/test_duplicate_detection_service.py::TestDuplicateDetectionServiceImpl::test_duplicate_detection_with_mock", "src/tests/test_duplicate_detection_service.py::TestDuplicateDetectionServiceImpl::test_duplicate_groups_filtering", "src/tests/test_duplicate_detection_service.py::TestDuplicateDetectionServiceImpl::test_file_collection", "src/tests/test_duplicate_detection_service.py::TestDuplicateDetectionServiceImpl::test_file_inclusion_logic", "src/tests/test_duplicate_detection_service.py::TestDuplicateDetectionServiceImpl::test_service_info", "src/tests/test_duplicate_detection_service.py::TestDuplicateDetectionServiceImpl::test_service_lifecycle", "src/tests/test_duplicate_detection_service.py::TestDuplicateDetectionServiceImpl::test_space_savings_calculation", "src/tests/test_duplicate_detection_service.py::TestDuplicateDetectionServiceImpl::test_task_management", "src/tests/test_duplicate_detection_service.py::TestDuplicateDetectionServiceImpl::test_validation_methods", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_actual_detection_execution_flow", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_algorithm_core", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_core_methods", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_edge_cases_extended", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_error_handling_comprehensive", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_monitoring_and_progress", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_progress_monitoring", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_request_validation", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_result_comprehensive", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_result_operations", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_result_processing", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_service_configuration", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_service_methods_coverage", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_task_lifecycle", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_task_management_extended", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_validation_comprehensive", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_detection_validation_methods", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_duplicate_group_operations_extended", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_duplicate_groups_management", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_error_handling_scenarios", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_file_collection_operations", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_hash_calculation", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_high_performance_detector_integration", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_service_health_and_status", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_space_calculation", "src/tests/test_duplicate_detection_service_extended.py::TestDuplicateDetectionServiceExtended::test_space_calculation_extended", "src/tests/test_duplicate_finder.py::PerformanceTestDuplicateFinder::test_performance", "src/tests/test_duplicate_finder.py::TestDuplicateFinder::test_find_duplicates", "src/tests/test_duplicate_finder.py::TestLocalDuplicateFinder::test_find_duplicates", "src/tests/test_duplicate_finder.py::TestLocalDuplicateFinder::test_interruption", "src/tests/test_dynamic_window_calculator.py::test_basic_window", "src/tests/test_dynamic_window_calculator.py::test_config_update", "src/tests/test_dynamic_window_calculator.py::test_custom_config", "src/tests/test_dynamic_window_calculator.py::test_error_handling", "src/tests/test_dynamic_window_calculator.py::test_file_size_window", "src/tests/test_dynamic_window_calculator.py::test_items_count_window", "src/tests/test_dynamic_window_calculator.py::test_mixed_data_types", "src/tests/test_dynamic_window_calculator.py::test_window_bounds", "src/tests/test_event_system.py::TestEventBus::test_async_event_handler", "src/tests/test_event_system.py::TestEventBus::test_concurrent_subscriptions", "src/tests/test_event_system.py::TestEventBus::test_error_handling", "src/tests/test_event_system.py::TestEventBus::test_event_bus_not_running", "src/tests/test_event_system.py::TestEventBus::test_event_bus_start_stop", "src/tests/test_event_system.py::TestEventBus::test_event_bus_stats", "src/tests/test_event_system.py::TestEventBus::test_event_data_immutability", "src/tests/test_event_system.py::TestEventBus::test_event_priority", "src/tests/test_event_system.py::TestEventBus::test_event_queue_overflow", "src/tests/test_event_system.py::TestEventBus::test_multiple_subscribers", "src/tests/test_event_system.py::TestEventBus::test_subscribe_and_publish", "src/tests/test_event_system.py::TestEventBus::test_subscription_cleanup", "src/tests/test_event_system.py::TestEventBus::test_unsubscribe", "src/tests/test_event_system.py::TestEventBus::test_unsubscribe_all", "src/tests/test_event_system.py::TestEventDefinitions::test_business_event_creation", "src/tests/test_event_system.py::TestEventDefinitions::test_event_data_creation", "src/tests/test_event_system.py::TestEventDefinitions::test_system_event_creation", "src/tests/test_event_system.py::TestEventDefinitions::test_ui_event_creation", "src/tests/test_event_system.py::TestEventSubscription::test_subscription_creation", "src/tests/test_event_system.py::TestEventSubscription::test_subscription_hash", "src/tests/test_file_operations.py::TestFileOperationsAsync::test_async_batch_delete", "src/tests/test_file_operations.py::TestFileOperationsAsync::test_async_batch_rename", "src/tests/test_file_operations.py::TestFileOperationsAsync::test_async_batch_with_exception", "src/tests/test_file_operations.py::TestFileOperationsAsync::test_cross_platform_path", "src/tests/test_file_operations.py::TestFileOperationsAsync::test_empty_and_illegal_path", "src/tests/test_file_operations.py::TestFileOperationsAsync::test_path_normalization_and_compatibility", "src/tests/test_file_operations_service.py::TestFileOperationsServiceImpl::test_batch_operations", "src/tests/test_file_operations_service.py::TestFileOperationsServiceImpl::test_conflict_resolution_rename", "src/tests/test_file_operations_service.py::TestFileOperationsServiceImpl::test_conflict_resolution_skip", "src/tests/test_file_operations_service.py::TestFileOperationsServiceImpl::test_copy_operation", "src/tests/test_file_operations_service.py::TestFileOperationsServiceImpl::test_create_directory_operation", "src/tests/test_file_operations_service.py::TestFileOperationsServiceImpl::test_delete_operation", "src/tests/test_file_operations_service.py::TestFileOperationsServiceImpl::test_move_operation", "src/tests/test_file_operations_service.py::TestFileOperationsServiceImpl::test_operation_history", "src/tests/test_file_operations_service.py::TestFileOperationsServiceImpl::test_operation_request_validation", "src/tests/test_file_operations_service.py::TestFileOperationsServiceImpl::test_preview_operations", "src/tests/test_file_operations_service.py::TestFileOperationsServiceImpl::test_rename_operation", "src/tests/test_file_operations_service.py::TestFileOperationsServiceImpl::test_service_info", "src/tests/test_file_operations_service.py::TestFileOperationsServiceImpl::test_service_lifecycle", "src/tests/test_file_scan_service.py::TestFileScanServiceImpl::test_concurrent_scans", "src/tests/test_file_scan_service.py::TestFileScanServiceImpl::test_directory_info", "src/tests/test_file_scan_service.py::TestFileScanServiceImpl::test_pause_resume_scan", "src/tests/test_file_scan_service.py::TestFileScanServiceImpl::test_scan_cancellation", "src/tests/test_file_scan_service.py::TestFileScanServiceImpl::test_scan_directories", "src/tests/test_file_scan_service.py::TestFileScanServiceImpl::test_scan_priority_setting", "src/tests/test_file_scan_service.py::TestFileScanServiceImpl::test_scan_request_validation", "src/tests/test_file_scan_service.py::TestFileScanServiceImpl::test_scan_time_estimation", "src/tests/test_file_scan_service.py::TestFileScanServiceImpl::test_scan_with_invalid_directory", "src/tests/test_file_scan_service.py::TestFileScanServiceImpl::test_service_info", "src/tests/test_file_scan_service.py::TestFileScanServiceImpl::test_service_lifecycle", "src/tests/test_file_scan_service.py::TestFileScanServiceImpl::test_supported_file_types", "src/tests/test_file_scan_service.py::TestFileScanServiceImpl::test_task_management", "src/tests/test_file_scan_service.py::TestFileScanServiceIntegration::test_concurrent_scans", "src/tests/test_file_scan_service.py::TestFileScanServiceIntegration::test_edge_cases", "src/tests/test_file_scan_service.py::TestFileScanServiceIntegration::test_error_handling", "src/tests/test_file_scan_service.py::TestFileScanServiceIntegration::test_event_publishing", "src/tests/test_file_scan_service.py::TestFileScanServiceIntegration::test_scan_cancellation", "src/tests/test_file_scan_service.py::TestFileScanServiceIntegration::test_service_lifecycle", "src/tests/test_file_scan_service_extended.py::TestFileScanServiceExtended::test_directory_estimation", "src/tests/test_file_scan_service_extended.py::TestFileScanServiceExtended::test_error_handling", "src/tests/test_file_scan_service_extended.py::TestFileScanServiceExtended::test_file_collection", "src/tests/test_file_scan_service_extended.py::TestFileScanServiceExtended::test_file_processing_operations", "src/tests/test_file_scan_service_extended.py::TestFileScanServiceExtended::test_scan_result_operations", "src/tests/test_file_scan_service_extended.py::TestFileScanServiceExtended::test_validation_operations", "src/tests/test_file_scanner.py::TestFileScannerAsync::test_async_scan_concurrent", "src/tests/test_file_scanner.py::TestFileScannerAsync::test_async_scan_single_dir", "src/tests/test_file_scanner.py::TestFileScannerAsync::test_async_scan_with_exception", "src/tests/test_file_tree_compatibility.py::test_fileinfo_fields_compatibility", "src/tests/test_file_tree_e2e.py::test_file_tree_panel_e2e", "src/tests/test_file_tree_edge_cases.py::test_fileinfo_deep_nesting", "src/tests/test_file_tree_edge_cases.py::test_fileinfo_empty_and_large", "src/tests/test_file_tree_edge_cases.py::test_fileinfo_special_paths", "src/tests/test_file_tree_index_service.py::test_build_and_query_index", "src/tests/test_file_tree_index_service.py::test_cache", "src/tests/test_file_tree_index_service.py::test_update_index", "src/tests/test_file_tree_integration.py::test_integration_scan_and_lazy_load", "src/tests/test_file_tree_performance.py::test_file_tree_index_performance", "src/tests/test_file_tree_ui.py::test_file_tree_panel_lazy_load", "src/tests/test_integration.py::TestDTOEventIntegration::test_concurrent_dto_processing", "src/tests/test_integration.py::TestDTOEventIntegration::test_dto_serialization_performance", "src/tests/test_integration.py::TestDTOEventIntegration::test_error_handling_integration", "src/tests/test_integration.py::TestDTOEventIntegration::test_multi_service_workflow", "src/tests/test_integration.py::TestDTOEventIntegration::test_performance_under_load", "src/tests/test_integration.py::TestDTOEventIntegration::test_rename_service_integration", "src/tests/test_integration.py::TestDTOEventIntegration::test_scan_workflow_integration", "src/tests/test_integration.py::TestSystemStability::test_file_operations_service_integration", "src/tests/test_integration.py::TestSystemStability::test_memory_leak_detection", "src/tests/test_integration.py::TestSystemStability::test_rename_service_integration", "src/tests/test_integration_services.py::TestServicesIntegration::test_service_health_monitoring", "src/tests/test_main_window_integration.py::test_initial_state", "src/tests/test_main_window_integration.py::test_menu_items", "src/tests/test_main_window_integration.py::test_multiple_open_attempts", "src/tests/test_main_window_integration.py::test_settings_window_close", "src/tests/test_main_window_integration.py::test_settings_window_position", "src/tests/test_main_window_integration.py::test_show_debounce_settings", "src/tests/test_main_window_integration.py::test_toolbar_buttons", "src/tests/test_main_window_integration.py::test_ui_interaction", "src/tests/test_optimized_task_panel.py::TestOptimizedTaskPanel::test_panel_initialization", "src/tests/test_progress_callback_debounce.py::test_callback_error_handling", "src/tests/test_progress_callback_debounce.py::test_callback_with_debounce", "src/tests/test_progress_callback_debounce.py::test_callback_without_debounce", "src/tests/test_progress_callback_debounce.py::test_dynamic_window_calculation", "src/tests/test_progress_callback_debounce.py::test_error_handling", "src/tests/test_progress_callback_debounce.py::test_force_update", "src/tests/test_progress_callback_debounce.py::test_initial_state", "src/tests/test_progress_callback_debounce.py::test_window_display_update", "src/tests/test_refresh_dedup_guard.py::test_basic_refresh", "src/tests/test_refresh_dedup_guard.py::test_complex_data_types", "src/tests/test_refresh_dedup_guard.py::test_dynamic_window", "src/tests/test_refresh_dedup_guard.py::test_force_refresh", "src/tests/test_refresh_dedup_guard.py::test_invalid_dynamic_window", "src/tests/test_refresh_dedup_guard.py::test_multiple_features", "src/tests/test_refresh_dedup_guard.py::test_non_serializable_data", "src/tests/test_refresh_dedup_guard.py::test_reset", "src/tests/test_rename_service.py::TestRenameRuleServiceImpl::test_add_prefix_rule", "src/tests/test_rename_service.py::TestRenameRuleServiceImpl::test_add_suffix_rule", "src/tests/test_rename_service.py::TestRenameRuleServiceImpl::test_change_case_rule", "src/tests/test_rename_service.py::TestRenameRuleServiceImpl::test_multiple_rules", "src/tests/test_rename_service.py::TestRenameRuleServiceImpl::test_regex_replace_rule", "src/tests/test_rename_service.py::TestRenameRuleServiceImpl::test_replace_text_rule", "src/tests/test_rename_service.py::TestRenameRuleServiceImpl::test_rule_template_management", "src/tests/test_rename_service.py::TestRenameRuleServiceImpl::test_rule_validation", "src/tests/test_rename_service.py::TestRenameServiceImpl::test_backup_functionality", "src/tests/test_rename_service.py::TestRenameServiceImpl::test_conflict_detection", "src/tests/test_rename_service.py::TestRenameServiceImpl::test_conflict_resolution", "src/tests/test_rename_service.py::TestRenameServiceImpl::test_execute_rename_preview_only", "src/tests/test_rename_service.py::TestRenameServiceImpl::test_preview_rename", "src/tests/test_rename_service.py::TestRenameServiceImpl::test_rename_request_validation", "src/tests/test_rename_service.py::TestRenameServiceImpl::test_service_info", "src/tests/test_rename_service.py::TestRenameServiceImpl::test_service_lifecycle", "src/tests/test_rename_service.py::TestRenameServiceImpl::test_unique_path_generation", "src/tests/test_rename_service.py::TestRenameServiceImpl::test_validation_methods", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_additional_service_methods", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_atomic_file_operations", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_backup_functionality", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_batch_operations", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_cancel_operations", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_different_rule_types", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_direct_service_methods", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_edge_cases_and_boundaries", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_error_handling", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_execute_rename_actual_execution_mode", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_execute_rename_core_functionality", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_execute_rename_core_task_execution", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_execute_rename_preview_mode", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_get_rule_templates_functionality", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_preview_rename_core_functionality", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_backup_and_rollback", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_conflict_detection_and_resolution", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_conflict_resolution", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_conflict_resolution_comprehensive", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_error_handling_comprehensive", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_execution_simulation", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_history_extended", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_history_operations", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_preview_comprehensive", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_progress_monitoring_detailed", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_request_validation", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_request_validation_comprehensive", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_result_operations", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_result_operations_comprehensive", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_rule_application", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_rule_application_comprehensive", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_rule_service_integration", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_service_advanced_features", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_service_core_methods", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_service_integration_comprehensive", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_transaction_comprehensive", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_transaction_management", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_undo_functionality", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rename_validation_comprehensive", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rule_operations", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rule_template_operations", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rule_template_operations_extended", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rule_template_save_and_load", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_rule_template_validation_and_error_handling", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_service_configuration_and_status", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_service_health_and_status", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_service_methods_coverage", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_simple_rename_operations", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_task_management", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_validate_rule_functionality", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_validation_edge_cases", "src/tests/test_rename_service_extended.py::TestRenameServiceExtended::test_validation_operations", "src/tests/test_service_container.py::TestEnhancedServiceContainer::test_all_services_health_report", "src/tests/test_service_container.py::TestEnhancedServiceContainer::test_async_service_resolution", "src/tests/test_service_container.py::TestEnhancedServiceContainer::test_container_clear", "src/tests/test_service_container.py::TestEnhancedServiceContainer::test_container_lifecycle", "src/tests/test_service_container.py::TestEnhancedServiceContainer::test_dependency_analysis", "src/tests/test_service_container.py::TestEnhancedServiceContainer::test_health_check_loop", "src/tests/test_service_container.py::TestEnhancedServiceContainer::test_service_configuration", "src/tests/test_service_container.py::TestEnhancedServiceContainer::test_service_health_check", "src/tests/test_service_container.py::TestEnhancedServiceContainer::test_service_health_check_failure", "src/tests/test_service_container.py::TestEnhancedServiceContainer::test_service_instance_registration", "src/tests/test_service_container.py::TestEnhancedServiceContainer::test_service_registration", "src/tests/test_service_container.py::TestEnhancedServiceContainer::test_service_resolution", "src/tests/test_service_container.py::TestEnhancedServiceContainer::test_service_scope", "src/tests/test_service_container.py::TestServiceContainerIntegration::test_enhanced_services_configuration", "src/tests/test_service_container.py::TestServiceContainerIntegration::test_global_container_access", "src/tests/test_settings_panel_integration.py::test_config_manager_integration", "src/tests/test_settings_panel_integration.py::test_debounce_panel_layout", "src/tests/test_settings_panel_integration.py::test_error_handling", "src/tests/test_settings_panel_integration.py::test_initial_state", "src/tests/test_settings_panel_integration.py::test_show_debounce_settings", "src/tests/test_settings_panel_integration.py::test_tab_names", "src/tests/test_settings_panel_integration.py::test_tab_switching", "src/tests/test_settings_panel_integration.py::test_ui_visibility", "src/tests/test_settings_panel_integration.py::test_window_display_update", "src/tests/test_video_analyzer.py::TestVideoAnalyzerAsync::test_async_batch_analyze", "src/tests/test_video_analyzer.py::TestVideoAnalyzerAsync::test_async_batch_with_exception", "src/tests/test_whitelist_panel_debounce.py::test_dynamic_window_calculation", "src/tests/test_whitelist_panel_debounce.py::test_error_handling", "src/tests/test_whitelist_panel_debounce.py::test_force_refresh", "src/tests/test_whitelist_panel_debounce.py::test_initial_state", "src/tests/test_whitelist_panel_debounce.py::test_update_with_debounce", "src/tests/test_whitelist_panel_debounce.py::test_update_without_debounce", "src/tests/test_whitelist_panel_debounce.py::test_window_display_update", "src/tests/ui/test_debounce_ui.py::TestDebounceSettingsPanelUI::test_all_buttons", "src/tests/ui/test_debounce_ui.py::TestDebounceSettingsPanelUI::test_feature_switches", "src/tests/ui/test_debounce_ui.py::TestDebounceSettingsPanelUI::test_global_switch", "src/tests/ui/test_debounce_ui.py::TestDebounceSettingsPanelUI::test_layout", "src/tests/ui/test_debounce_ui.py::TestDebounceSettingsPanelUI::test_window_display", "src/tests/ui/test_debounce_ui.py::TestMainWindowIntegration::test_menu_integration", "src/tests/ui/test_debounce_ui.py::TestMainWindowIntegration::test_multiple_window_open", "src/tests/ui/test_debounce_ui.py::TestMainWindowIntegration::test_settings_window", "src/tests/ui/test_debounce_ui.py::TestMainWindowIntegration::test_toolbar_integration", "src/tests/ui/test_debounce_ui.py::TestSettingsPanelIntegration::test_debounce_panel_integration", "src/tests/ui/test_debounce_ui.py::TestSettingsPanelIntegration::test_window_display_update", "test_actual_file_tree.py::test_actual_file_tree", "test_all_panels.py::test_all_panels", "test_async_file_tree_loading.py::test_async_file_tree_loading", "test_async_task_duration.py::test_async_task_duration_logging", "test_async_task_duration.py::test_cancelled_task", "test_async_task_duration.py::test_duration_formatting", "test_async_task_duration.py::test_failing_task", "test_async_task_duration.py::test_long_task", "test_async_task_duration.py::test_medium_task", "test_async_task_duration.py::test_short_task", "test_async_task_duration.py::test_unified_task_manager_duration", "test_async_task_id_fix.py::test_async_function", "test_async_task_id_fix.py::test_async_task_manager", "test_async_task_id_fix.py::test_task_id_generation", "test_async_task_id_fix.py::test_unified_task_manager", "test_async_task_manager.py::test_basic_async_task", "test_async_task_manager.py::test_error_handling", "test_async_task_manager.py::test_interrupt_async_task", "test_async_task_manager.py::test_multiple_async_tasks", "test_async_task_manager.py::test_task_status_monitoring", "test_basic_interrupt.py::test_basic_interrupt", "test_clean_exit.py::test_clean_exit", "test_complete_display_fix.py::test_complete_display_fix", "test_complete_startup_solution.py::test_config_loading", "test_complete_startup_solution.py::test_interrupt_manager_integration", "test_complete_startup_solution.py::test_settings_panel_integration", "test_complete_startup_solution.py::test_startup_flow_simulation", "test_database_duplicate_integration.py::test_database_duplicate_integration", "test_db_connection_fix.py::test_db_connection", "test_db_monitor.py::test_db_status_monitor", "test_duplicate_buttons.py::test_duplicate_buttons", "test_duplicate_buttons_enhanced.py::test_duplicate_buttons_enhanced", "test_duplicate_buttons_fixed.py::test_duplicate_buttons_fixed", "test_duplicate_check_flow.py::test_database_duplicate_check", "test_duplicate_check_flow.py::test_duplicate_check_flow", "test_duplicate_check_flow.py::test_error_handling", "test_duplicate_check_flow.py::test_parameter_validation", "test_duplicate_check_flow.py::test_ui_duplicate_check", "test_duplicate_complete.py::test_duplicate_panel", "test_duplicate_diagnosis.py::test_file_scanner", "test_duplicate_fix.py::test_duplicate_fix", "test_duplicate_folder_optimization.py::test_duplicate_detection_optimization", "test_duplicate_folder_optimization.py::test_log_frequency_control", "test_duplicate_folder_optimization.py::test_performance_comparison", "test_duplicate_integration.py::test_duplicate_integration", "test_duplicate_load_fix.py::test_duplicate_load_fix", "test_duplicate_with_scan.py::test_duplicate_with_scan", "test_enhanced_interrupt.py::test_enhanced_interrupt_system", "test_enhanced_interrupt.py::test_file_scanner_with_enhanced_interrupt", "test_enhanced_interrupt.py::test_performance_optimization", "test_file_id.py::test_database_operations", "test_file_id.py::test_file_info_creation", "test_file_id.py::test_file_scanner", "test_file_id.py::test_file_tree_integration", "test_file_info_issue.py::test_file_info_constructor", "test_file_info_issue.py::test_scan_with_database", "test_file_tree_duplicate_fix.py::test_directory_detection", "test_file_tree_duplicate_fix.py::test_file_info_processing", "test_file_tree_duplicate_fix.py::test_path_validation", "test_file_tree_fix.py::test_depth_calculation", "test_file_tree_fix.py::test_path_processing", "test_file_tree_fix.py::test_traditional_mode", "test_file_tree_hierarchy.py::test_file_tree_hierarchy", "test_file_tree_hierarchy.py::test_path_normalization", "test_final_display_fix.py::test_final_display_fix", "test_final_layout.py::test_final_layout", "test_fixes_verification.py::test_config_loader_fix", "test_fixes_verification.py::test_enhanced_task_overview_panel", "test_fixes_verification.py::test_task_lifecycle", "test_folder_display_fix.py::test_folder_display_fix", "test_folder_structure_fix.py::test_folder_structure_fix", "test_hash_calculation_fix.py::test_database_query", "test_hash_calculation_fix.py::test_file_type_detection", "test_hash_calculation_fix.py::test_hash_calculation_filter", "test_hash_calculation_fix.py::test_hash_calculator_safety", "test_hash_monitoring.py::test_custom_widgets", "test_hash_monitoring.py::test_db_status_monitor", "test_improved_display_fix.py::test_improved_display_fix", "test_interrupt_functionality.py::test_interrupt_functionality", "test_interrupt_functionality.py::test_unified_task_manager", "test_interrupt_hash_calculation.py::test_hash_calculation_interrupt", "test_interrupt_hash_calculation.py::test_with_existing_calculation", "test_interrupt_improved.py::test_interrupt_during_execution", "test_interrupt_improved.py::test_scan_simulation", "test_interruptible_task.py::test_interruptible_task", "test_interruptible_task.py::test_ui_integration", "test_layout_improvements.py::test_combined_layout", "test_layout_improvements.py::test_resizable_paned_window", "test_layout_improvements.py::test_task_overview_panel", "test_log_buffer.py::test_log_buffer_basic", "test_log_buffer.py::test_log_buffer_concurrent", "test_log_buffer.py::test_log_buffer_frequency_limit", "test_log_buffer.py::test_log_buffer_task_completion", "test_logging_and_settings.py::test_logging_system", "test_logging_and_settings.py::test_settings_panel", "test_logging_system.py::test_basic_logging", "test_logging_system.py::test_concurrent_logging", "test_logging_system.py::test_module_logging", "test_logging_system.py::test_ui_log_callback", "test_main_app_duplicate.py::test_main_app_duplicate", "test_optimized_file_tree_insertion.py::test_file_tree_insertion", "test_path_based_duplicate_prevention.py::test_path_based_duplicate_prevention", "test_place_layout.py::test_place_layout", "test_progress_system.py::test_progress_callback_creation", "test_progress_system.py::test_progress_manager", "test_progress_system.py::test_task_status_transitions", "test_real_interrupt.py::test_real_interrupt", "test_root_directory_issue.py::test_root_directory_issue", "test_root_directory_simple.py::test_root_directory_simple", "test_scan_database_fix.py::test_scan_with_database_fix", "test_scan_database_fix.py::test_scan_without_database", "test_scan_stop_function.py::test_scan_stop_function", "test_scan_timeout_fix.py::test_scan_timeout_fix", "test_settings.py::test_settings_panel", "test_simple_display_fix.py::test_simple_display_fix", "test_simple_interrupt.py::test_basic_interrupt", "test_smart_timeout.py::test_smart_timeout", "test_smart_timeout.py::test_timeout_formula", "test_startup_interrupt.py::test_config_based_auto_load", "test_startup_interrupt.py::test_startup_interrupt_simulation", "test_stats_fix.py::test_stats_display", "test_status_bar_fixed.py::test_status_bar_fixed", "test_status_bar_layout.py::test_status_bar", "test_task_execution.py::test_task_execution", "test_task_execution.py::test_task_interruption", "test_tb_background_scan.py::test_scan_mode_selection", "test_tb_background_scan.py::test_tb_background_scan_detection", "test_tb_files_timeout.py::test_improved_timeout_calculation", "test_tb_timeout_improved.py::test_improved_timeout", "test_ui_functionality.py::test_log_callback", "test_ui_functionality.py::test_ui_components", "tests/architecture/test_config_loader_architecture.py::test_config_format_enum", "tests/architecture/test_config_loader_architecture.py::test_config_loader_initialization", "tests/architecture/test_config_loader_architecture.py::test_config_loader_nonexistent_dir", "tests/architecture/test_config_loader_architecture.py::test_config_metadata", "tests/architecture/test_config_loader_architecture.py::test_error_hierarchy", "tests/architecture/test_config_loader_architecture.py::test_get_config_path", "tests/architecture/test_config_loader_architecture.py::test_list_configs", "tests/architecture/test_config_loader_architecture.py::test_load_invalid_yaml", "tests/architecture/test_config_loader_architecture.py::test_load_nonexistent_config", "tests/architecture/test_config_loader_architecture.py::test_save_and_load_config", "tests/architecture/test_config_loader_architecture.py::test_update_config", "tests/architecture/test_dependency_injection_architecture.py::test_circular_dependency", "tests/architecture/test_dependency_injection_architecture.py::test_clear_container", "tests/architecture/test_dependency_injection_architecture.py::test_dependency_injection", "tests/architecture/test_dependency_injection_architecture.py::test_error_hierarchy", "tests/architecture/test_dependency_injection_architecture.py::test_invalid_lifetime", "tests/architecture/test_dependency_injection_architecture.py::test_register_and_resolve_singleton", "tests/architecture/test_dependency_injection_architecture.py::test_register_and_resolve_transient", "tests/architecture/test_dependency_injection_architecture.py::test_register_factory", "tests/architecture/test_dependency_injection_architecture.py::test_register_instance", "tests/architecture/test_dependency_injection_architecture.py::test_service_descriptor", "tests/architecture/test_dependency_injection_architecture.py::test_service_lifetime_enum", "tests/architecture/test_dependency_injection_architecture.py::test_service_not_found", "tests/architecture/test_dependency_injection_architecture.py::test_service_resolution_error", "tests/architecture/test_file_scanner_architecture.py::test_directory_scan_error_handling", "tests/architecture/test_file_scanner_architecture.py::test_file_info_creation", "tests/architecture/test_file_scanner_architecture.py::test_file_info_string_representation", "tests/architecture/test_file_scanner_architecture.py::test_file_scanner_error_hierarchy", "tests/architecture/test_file_scanner_architecture.py::test_file_scanner_initialization", "tests/architecture/test_file_scanner_architecture.py::test_file_scanner_junk_detection", "tests/architecture/test_file_scanner_architecture.py::test_file_scanner_video_detection", "tests/architecture/test_file_scanner_architecture.py::test_file_scanner_whitelist_detection", "tests/architecture/test_file_scanner_architecture.py::test_hash_calculation_error", "tests/architecture/test_file_scanner_architecture.py::test_video_metadata", "tests/architecture/test_rule_engine_architecture.py::test_batch_rule_application", "tests/architecture/test_rule_engine_architecture.py::test_rule_application", "tests/architecture/test_rule_engine_architecture.py::test_rule_config_dataclass", "tests/architecture/test_rule_engine_architecture.py::test_rule_engine_initialization", "tests/architecture/test_rule_engine_architecture.py::test_rule_engine_load_invalid_file", "tests/architecture/test_rule_engine_architecture.py::test_rule_engine_validation", "tests/architecture/test_rule_engine_architecture.py::test_special_chars_handling", "tests/architecture/test_rule_engine_architecture.py::test_video_file_handling", "tests/architecture/test_system_architecture.py::test_async_manager", "tests/architecture/test_system_architecture.py::test_dependency_injection", "tests/architecture/test_system_architecture.py::test_event_system", "tests/architecture/test_system_architecture.py::test_interfaces", "tests/architecture/test_system_architecture.py::test_logger", "tests/architecture/test_system_architecture.py::test_ui_factory", "tests/integration/test_mongodb_integration.py::MongoDBUITest::test_ui_containment", "tests/integration/test_mongodb_integration.py::test_event_handler", "tests/integration/test_mongodb_integration.py::test_progress_callback", "tests/performance/test_optimization_benchmark.py::test_rescan_same_directory", "tests/performance/test_optimization_benchmark.py::test_scan_and_update_database", "tests/stress/test_bulk_rename_performance.py::test_bulk_rename_performance", "tests/test_file_operations_error_handling.py::TestFileOperationsErrorHandling::test_backup_file_async_cancelled_error", "tests/test_file_operations_error_handling.py::TestFileOperationsErrorHandling::test_backup_file_async_io_error", "tests/test_file_operations_error_handling.py::TestFileOperationsErrorHandling::test_backup_file_async_metadata_error", "tests/test_file_operations_error_handling.py::TestFileOperationsErrorHandling::test_backup_file_async_no_backup_dir", "tests/test_file_operations_error_handling.py::TestFileOperationsErrorHandling::test_backup_file_async_nonexistent_file", "tests/test_file_operations_error_handling.py::TestFileOperationsErrorHandling::test_backup_file_async_permission_error", "tests/test_file_operations_error_handling.py::TestFileOperationsErrorHandling::test_batch_operations_partial_failure", "tests/test_file_operations_error_handling.py::TestFileOperationsErrorHandling::test_rename_file_nonexistent", "tests/test_file_operations_error_handling.py::TestFileOperationsErrorHandling::test_rename_file_target_exists", "tests/test_file_scanner.py::TestFileScanner::test_find_duplicate_files", "tests/test_file_scanner.py::TestFileScanner::test_find_junk_files_async", "tests/test_file_scanner.py::TestFileScanner::test_find_whitelist_files", "tests/test_file_scanner.py::TestFileScanner::test_format_file_size", "tests/test_file_scanner.py::TestFileScanner::test_scan_directory", "tests/test_lifecycle.py::TestApplicationLifecycle::test_async_manager_shutdown", "tests/test_lifecycle.py::TestApplicationLifecycle::test_dependency_injection_initialization", "tests/test_lifecycle.py::TestApplicationLifecycle::test_event_system_shutdown", "tests/test_lifecycle.py::TestApplicationLifecycle::test_graceful_shutdown", "tests/test_lifecycle.py::TestApplicationLifecycle::test_ui_cleanup", "tests/test_rule_engine.py::TestRuleEngine::test_apply_case_transformation", "tests/test_rule_engine.py::TestRuleEngine::test_apply_pattern", "tests/test_rule_engine.py::TestRuleEngine::test_is_video_file", "tests/test_rule_engine.py::TestRuleEngine::test_load_rules", "tests/test_rule_engine.py::TestRuleEngine::test_remove_special_chars", "tests/test_rule_engine.py::TestRuleEngine::test_rename_file", "tests/test_rule_engine.py::TestRuleEngine::test_rule_persistence", "tests/test_rule_engine.py::TestRuleEngine::test_rule_validation", "tests/test_rule_engine.py::TestRuleEngine::test_save_rules", "tests/ui/test_factory.py::TestUIFactory::test_create_duplicate_panel", "tests/ui/test_factory.py::TestUIFactory::test_create_rename_panel", "tests/ui/test_main_window.py::test_file_tree_update", "tests/ui/test_main_window.py::test_main_window_initialization", "tests/ui/test_main_window_components.py::test_duplicate_panel_exists", "tests/ui/test_main_window_components.py::test_file_tree_panel_exists", "tests/ui/test_main_window_components.py::test_junk_panel_exists", "tests/ui/test_main_window_components.py::test_left_frame_exists", "tests/ui/test_main_window_components.py::test_main_frame_exists", "tests/ui/test_main_window_components.py::test_rename_panel_exists", "tests/ui/test_main_window_components.py::test_right_frame_exists", "tests/ui/test_main_window_components.py::test_settings_panel_exists", "tests/ui/test_main_window_components.py::test_status_bar_exists", "tests/ui/test_main_window_components.py::test_whitelist_panel_exists", "tests/ui/test_ui_controls.py::TestUIControls::test_function_panels", "tests/ui/test_ui_controls.py::TestUIControls::test_main_window_controls", "tests/ui/test_ui_controls.py::TestUIControls::test_notebook_tabs", "tests/ui/test_ui_controls.py::TestUIControls::test_status_bar", "tests/ui/test_ui_controls.py::TestUIControls::test_theme_application"]