#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文件操作DTO模块

定义文件操作相关的数据传输对象，遵循RULE-001单一职责原则
包含文件操作请求、结果、进度等DTO类

作者: SmartFileManager开发团队
日期: 2024-01-20
版本: 2.0.0
"""

import time
import uuid
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Union
from enum import Enum
from pathlib import Path

from .base_dto import BaseDTO, ProgressUpdate, ErrorInfo


class FileOperationType(Enum):
    """文件操作类型枚举"""
    COPY = "copy"
    MOVE = "move"
    DELETE = "delete"
    RENAME = "rename"
    CREATE_DIRECTORY = "create_directory"
    BACKUP = "backup"
    RESTORE = "restore"


class FileOperationStatus(Enum):
    """文件操作状态枚举"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    SKIPPED = "skipped"


class ConflictResolution(Enum):
    """冲突解决策略枚举"""
    SKIP = "skip"
    OVERWRITE = "overwrite"
    RENAME = "rename"
    BACKUP = "backup"
    ASK_USER = "ask_user"


@dataclass(frozen=True)
class FileOperationItem(BaseDTO):
    """单个文件操作项"""
    source_path: str
    target_path: str
    operation_type: FileOperationType
    file_size: int = 0
    checksum: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def validate(self) -> List[str]:
        """验证文件操作项"""
        errors = []
        
        if not self.source_path:
            errors.append("源路径不能为空")
        
        # 删除操作不需要目标路径
        if not self.target_path and self.operation_type != FileOperationType.DELETE:
            errors.append("目标路径不能为空")
        
        if self.operation_type not in FileOperationType:
            errors.append("无效的操作类型")
        
        if self.file_size < 0:
            errors.append("文件大小不能为负数")
        
        return errors


@dataclass(frozen=True)
class FileOperationRequest(BaseDTO):
    """文件操作请求DTO"""
    task_id: str
    operations: List[FileOperationItem]
    conflict_resolution: ConflictResolution = ConflictResolution.ASK_USER
    create_backup: bool = True
    verify_checksum: bool = False
    preserve_permissions: bool = True
    preserve_timestamps: bool = True
    max_concurrent_operations: int = 4
    timeout_seconds: int = 300
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.task_id:
            # 使用object.__setattr__来设置frozen dataclass的属性
            object.__setattr__(self, 'task_id', str(uuid.uuid4()))
    
    def validate(self) -> List[str]:
        """验证文件操作请求"""
        errors = []
        
        if not self.task_id:
            errors.append("任务ID不能为空")
        
        if not self.operations:
            errors.append("操作列表不能为空")
        
        if self.max_concurrent_operations <= 0:
            errors.append("最大并发操作数必须大于0")
        
        if self.timeout_seconds <= 0:
            errors.append("超时时间必须大于0")
        
        # 验证每个操作项
        for i, operation in enumerate(self.operations):
            operation_errors = operation.validate()
            for error in operation_errors:
                errors.append(f"操作项{i+1}: {error}")
        
        return errors


@dataclass
class FileOperationResult:
    """单个文件操作结果"""
    operation_item: FileOperationItem
    status: FileOperationStatus
    start_time: float = 0.0
    end_time: float = 0.0
    bytes_processed: int = 0
    error_message: Optional[str] = None
    backup_path: Optional[str] = None
    actual_target_path: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def duration(self) -> float:
        """操作持续时间（秒）"""
        if self.end_time > 0 and self.start_time > 0:
            return self.end_time - self.start_time
        return 0.0
    
    @property
    def success(self) -> bool:
        """操作是否成功"""
        return self.status == FileOperationStatus.COMPLETED
    
    def validate(self) -> List[str]:
        """验证操作结果"""
        errors = []

        if not self.operation_item:
            errors.append("操作项不能为空")

        if self.status not in FileOperationStatus:
            errors.append("无效的操作状态")

        if self.bytes_processed < 0:
            errors.append("处理字节数不能为负数")

        return errors

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "operation_item": self.operation_item.to_dict() if hasattr(self.operation_item, 'to_dict') else str(self.operation_item),
            "status": self.status.value if hasattr(self.status, 'value') else str(self.status),
            "start_time": self.start_time,
            "end_time": self.end_time,
            "bytes_processed": self.bytes_processed,
            "error_message": self.error_message,
            "backup_path": self.backup_path,
            "actual_target_path": self.actual_target_path,
            "metadata": self.metadata,
            "duration": self.duration,
            "success": self.success
        }


@dataclass
class FileOperationBatchResult:
    """批量文件操作结果DTO"""
    task_id: str
    total_operations: int
    successful_operations: int = 0
    failed_operations: int = 0
    skipped_operations: int = 0
    cancelled_operations: int = 0
    start_time: float = field(default_factory=time.time)
    end_time: float = 0.0
    total_bytes_processed: int = 0
    operation_results: List[FileOperationResult] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def duration(self) -> float:
        """总持续时间（秒）"""
        if self.end_time > 0:
            return self.end_time - self.start_time
        return time.time() - self.start_time
    
    @property
    def success_rate(self) -> float:
        """成功率（百分比）"""
        if self.total_operations == 0:
            return 0.0
        return (self.successful_operations / self.total_operations) * 100.0
    
    @property
    def is_completed(self) -> bool:
        """是否已完成"""
        return (self.successful_operations + self.failed_operations + 
                self.skipped_operations + self.cancelled_operations) >= self.total_operations
    
    def validate(self) -> List[str]:
        """验证批量操作结果"""
        errors = []

        if not self.task_id:
            errors.append("任务ID不能为空")

        if self.total_operations < 0:
            errors.append("总操作数不能为负数")

        if self.successful_operations < 0:
            errors.append("成功操作数不能为负数")

        if self.failed_operations < 0:
            errors.append("失败操作数不能为负数")

        if self.total_bytes_processed < 0:
            errors.append("总处理字节数不能为负数")

        return errors

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "task_id": self.task_id,
            "total_operations": self.total_operations,
            "successful_operations": self.successful_operations,
            "failed_operations": self.failed_operations,
            "skipped_operations": self.skipped_operations,
            "cancelled_operations": self.cancelled_operations,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "total_bytes_processed": self.total_bytes_processed,
            "operation_results": [result.to_dict() for result in self.operation_results],
            "errors": self.errors,
            "warnings": self.warnings,
            "metadata": self.metadata,
            "duration": self.duration,
            "success_rate": self.success_rate,
            "is_completed": self.is_completed
        }


@dataclass
class FileOperationProgress:
    """文件操作进度DTO"""
    task_id: str
    progress: float = 0.0
    status_message: str = ""
    processed_items: int = 0
    total_items: int = 0
    current_operation: Optional[FileOperationItem] = None
    current_file_progress: float = 0.0
    bytes_per_second: float = 0.0
    estimated_time_remaining: float = 0.0
    active_operations: int = 0
    
    def validate(self) -> List[str]:
        """验证进度信息"""
        errors = []

        if not self.task_id:
            errors.append("任务ID不能为空")

        if self.progress < 0 or self.progress > 100:
            errors.append("进度必须在0-100之间")

        if self.current_file_progress < 0 or self.current_file_progress > 100:
            errors.append("当前文件进度必须在0-100之间")

        if self.bytes_per_second < 0:
            errors.append("传输速度不能为负数")

        if self.estimated_time_remaining < 0:
            errors.append("预估剩余时间不能为负数")

        if self.active_operations < 0:
            errors.append("活动操作数不能为负数")

        return errors

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "task_id": self.task_id,
            "progress": self.progress,
            "status_message": self.status_message,
            "processed_items": self.processed_items,
            "total_items": self.total_items,
            "current_operation": self.current_operation.to_dict() if self.current_operation else None,
            "current_file_progress": self.current_file_progress,
            "bytes_per_second": self.bytes_per_second,
            "estimated_time_remaining": self.estimated_time_remaining,
            "active_operations": self.active_operations
        }


@dataclass(frozen=True)
class FileOperationConfig(BaseDTO):
    """文件操作配置DTO"""
    max_concurrent_operations: int = 4
    buffer_size: int = 64 * 1024  # 64KB
    verify_checksums: bool = False
    create_backups: bool = True
    preserve_permissions: bool = True
    preserve_timestamps: bool = True
    retry_attempts: int = 3
    retry_delay: float = 1.0
    timeout_seconds: int = 300
    progress_update_interval: float = 0.5
    
    def validate(self) -> List[str]:
        """验证配置"""
        errors = []
        
        if self.max_concurrent_operations <= 0:
            errors.append("最大并发操作数必须大于0")
        
        if self.buffer_size <= 0:
            errors.append("缓冲区大小必须大于0")
        
        if self.retry_attempts < 0:
            errors.append("重试次数不能为负数")
        
        if self.retry_delay < 0:
            errors.append("重试延迟不能为负数")
        
        if self.timeout_seconds <= 0:
            errors.append("超时时间必须大于0")
        
        if self.progress_update_interval <= 0:
            errors.append("进度更新间隔必须大于0")
        
        return errors


# 常量定义（遵循QUAL-001规则）
DEFAULT_BUFFER_SIZE = 64 * 1024  # 64KB
DEFAULT_MAX_CONCURRENT_OPERATIONS = 4
DEFAULT_TIMEOUT_SECONDS = 300
DEFAULT_RETRY_ATTEMPTS = 3
DEFAULT_RETRY_DELAY = 1.0
DEFAULT_PROGRESS_UPDATE_INTERVAL = 0.5

# 文件大小阈值
SMALL_FILE_THRESHOLD = 1024 * 1024  # 1MB
LARGE_FILE_THRESHOLD = 100 * 1024 * 1024  # 100MB

# 操作优先级
OPERATION_PRIORITY = {
    FileOperationType.DELETE: 1,
    FileOperationType.RENAME: 2,
    FileOperationType.MOVE: 3,
    FileOperationType.COPY: 4,
    FileOperationType.BACKUP: 5,
    FileOperationType.CREATE_DIRECTORY: 6,
    FileOperationType.RESTORE: 7,
}
