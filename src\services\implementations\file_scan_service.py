#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文件扫描服务实现

基于新架构重构的文件扫描服务，遵循RULE-001和RULE-003
使用DTO进行数据传输，通过事件总线进行通信
"""

import asyncio
import os
import time
import uuid
from pathlib import Path
from typing import AsyncGenerator, Optional, List, Dict, Any
from concurrent.futures import ThreadPoolExecutor

from src.services.interfaces import (
    IFileScanService, IFileIndexService, IFileHashService,
    BaseServiceImpl, ServiceStatus, ScanPriority
)
from src.data.dto.scan_dto import (
    ScanRequest, ScanResult, FileInfo, ScanStatus, ScanType
)
from src.data.dto.base_dto import ProgressUpdate, ErrorInfo
from src.data.dto.interface_dto import DirectoryInfo, FileCollection, ProcessingResult
from src.ui.events.event_bus import IEventBus
from src.ui.events.event_definitions import (
    create_business_event, BusinessEventType, EventPriority
)

# 常量定义
DEFAULT_MAX_WORKERS = 4
DEFAULT_BATCH_SIZE = 100
DEFAULT_SCAN_TIMEOUT = 3600.0  # 1小时
ESTIMATED_FILES_PER_DIR = 50  # 估算每个目录的文件数
ESTIMATED_DIR_SIZE_MB = 1  # 估算每个目录的大小(MB)
FILE_PROCESSING_TIME_MS = 0.001  # 每个文件的处理时间(秒)
PROGRESS_CHECK_INTERVAL = 0.1  # 进度检查间隔(秒)
KILOBYTE_SIZE = 1024  # 1KB的字节数
MEGABYTE_SIZE = KILOBYTE_SIZE * KILOBYTE_SIZE  # 1MB的字节数


class FileScanServiceImpl(BaseServiceImpl, IFileScanService):
    """
    文件扫描服务实现
    
    遵循RULE-001: 模块职责单一原则 - 只负责文件扫描
    遵循RULE-003: 事件驱动通信规范 - 通过事件总线通信
    """
    
    def __init__(self, event_bus: IEventBus, max_workers: int = DEFAULT_MAX_WORKERS):
        super().__init__("FileScanService", "2.0.0")
        self._event_bus = event_bus
        self._max_workers = max_workers
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
        self._scan_tasks: Dict[str, asyncio.Task] = {}
        self._scan_results: Dict[str, ScanResult] = {}
        self._scan_progress: Dict[str, ProgressUpdate] = {}
        
    async def start_service(self) -> bool:
        """启动服务"""
        if await super().start_service():
            # 发布服务启动事件
            event = create_business_event(
                BusinessEventType.SCAN_STARTED,  # 使用现有的事件类型
                self.get_service_name(),
                {"service_version": self.get_service_version(), "action": "service_started"}
            )
            self._event_bus.publish("SERVICE_STARTED", event)
            return True
        return False
    
    async def stop_service(self) -> bool:
        """停止服务"""
        # 取消所有活动的扫描任务
        await self.cancel_all_tasks()
        
        # 关闭线程池
        self._executor.shutdown(wait=True)
        
        if await super().stop_service():
            # 发布服务停止事件
            event = create_business_event(
                BusinessEventType.SCAN_COMPLETED,  # 使用现有的事件类型
                self.get_service_name(),
                {"action": "service_stopped"}
            )
            self._event_bus.publish("SERVICE_STOPPED", event)
            return True
        return False
    
    async def scan_directories(self, request: ScanRequest) -> AsyncGenerator[ProgressUpdate, None]:
        """
        异步扫描目录
        
        遵循RULE-005: 长时间运行的操作实现为异步任务
        """
        # 验证请求
        validation_errors = await self.validate_scan_request(request)
        if validation_errors:
            error_info = ErrorInfo(
                code="SCAN_VALIDATION_FAILED",
                message="扫描请求验证失败",
                details="; ".join(validation_errors)
            )
            raise ValueError(error_info.message)
        
        # 发布扫描开始事件
        event = create_business_event(
            BusinessEventType.SCAN_STARTED,
            self.get_service_name(),
            {
                "task_id": request.task_id,
                "directories": request.directories,
                "scan_type": request.scan_type.value
            }
        )
        self._event_bus.publish("SCAN_STARTED", event)
        
        # 创建扫描任务
        scan_task = asyncio.create_task(self._execute_scan(request))
        self._scan_tasks[request.task_id] = scan_task
        
        try:
            # 异步生成进度更新
            async for progress in self._monitor_scan_progress(request.task_id):
                yield progress
                
                # 发布进度事件
                progress_event = create_business_event(
                    BusinessEventType.SCAN_PROGRESS,
                    self.get_service_name(),
                    progress.to_dict()
                )
                self._event_bus.publish("SCAN_PROGRESS", progress_event)
        
        except Exception as e:
            # 处理扫描错误
            error_info = ErrorInfo(
                code="SCAN_EXECUTION_FAILED",
                message=f"扫描执行失败: {str(e)}",
                details=f"任务ID: {request.task_id}"
            )
            
            error_event = create_business_event(
                BusinessEventType.SCAN_FAILED,
                self.get_service_name(),
                error_info.to_dict(),
                EventPriority.HIGH
            )
            self._event_bus.publish("SCAN_FAILED", error_event)
            raise
        
        finally:
            # 清理任务
            if request.task_id in self._scan_tasks:
                del self._scan_tasks[request.task_id]
    
    async def get_scan_result(self, task_id: str) -> Optional[ScanResult]:
        """获取扫描结果"""
        return self._scan_results.get(task_id)
    
    async def pause_scan(self, task_id: str) -> bool:
        """暂停扫描任务"""
        if task_id in self._scan_tasks:
            # 发布暂停事件
            event = create_business_event(
                BusinessEventType.SCAN_PAUSED,
                self.get_service_name(),
                {"task_id": task_id}
            )
            self._event_bus.publish("SCAN_PAUSED", event)
            return True
        return False
    
    async def resume_scan(self, task_id: str) -> bool:
        """恢复扫描任务"""
        if task_id in self._scan_tasks:
            # 发布恢复事件
            event = create_business_event(
                BusinessEventType.SCAN_RESUMED,
                self.get_service_name(),
                {"task_id": task_id}
            )
            self._event_bus.publish("SCAN_RESUMED", event)
            return True
        return False
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消扫描任务"""
        if task_id in self._scan_tasks:
            task = self._scan_tasks[task_id]
            task.cancel()
            
            try:
                await task
            except asyncio.CancelledError:
                # 任务被正常取消
                self._event_bus.publish("SCAN_TASK_CANCELLED",
                    create_business_event(
                        BusinessEventType.SCAN_CANCELLED,
                        self.get_service_name(),
                        {"task_id": task_id, "reason": "user_cancelled"}
                    )
                )
            
            # 清理任务数据
            self._scan_tasks.pop(task_id, None)
            self._scan_progress.pop(task_id, None)
            
            # 发布取消事件
            event = create_business_event(
                BusinessEventType.SCAN_CANCELLED,
                self.get_service_name(),
                {"task_id": task_id}
            )
            self._event_bus.publish("SCAN_CANCELLED", event)
            return True
        
        return False
    
    async def get_scan_progress(self, task_id: str) -> Optional[ProgressUpdate]:
        """获取扫描进度"""
        return self._scan_progress.get(task_id)
    
    async def estimate_scan_time(self, request: ScanRequest) -> float:
        """估算扫描时间"""
        total_size = 0
        total_files = 0

        for directory in request.directories:
            dir_stats = await self._estimate_directory_stats(directory, request.recursive)
            total_files += dir_stats["file_count"]
            total_size += dir_stats["total_size"]

        # 基于文件数量和大小估算时间（经验公式）
        estimated_seconds = (total_files * FILE_PROCESSING_TIME_MS) + (total_size / (MEGABYTE_SIZE * 10))
        return max(estimated_seconds, 1.0)  # 至少1秒

    async def _estimate_directory_stats(self, directory: str, recursive: bool) -> Dict[str, int]:
        """估算目录统计信息"""
        try:
            dir_path = Path(directory)
            if not dir_path.exists() or not dir_path.is_dir():
                return {"file_count": 0, "total_size": 0}

            file_count = 0
            total_size = 0

            for item in dir_path.iterdir():
                if item.is_file():
                    file_count += 1
                    total_size += item.stat().st_size
                elif item.is_dir() and recursive:
                    # 简单估算子目录
                    file_count += ESTIMATED_FILES_PER_DIR
                    total_size += ESTIMATED_DIR_SIZE_MB * MEGABYTE_SIZE

            return {"file_count": file_count, "total_size": total_size}

        except Exception:
            return {"file_count": 0, "total_size": 0}
    
    async def get_directory_info(self, directory_path: str) -> Optional[DirectoryInfo]:
        """
        获取目录信息

        Args:
            directory_path: 目录路径

        Returns:
            DirectoryInfo: 目录信息DTO，如果目录不存在返回None
        """
        return DirectoryInfo.from_path(directory_path)
    
    async def validate_scan_request(self, request: ScanRequest) -> List[str]:
        """验证扫描请求"""
        errors = []
        
        # 使用DTO的内置验证
        dto_errors = request.validate()
        errors.extend(dto_errors)
        
        # 额外的业务验证
        for directory in request.directories:
            dir_path = Path(directory)
            if not dir_path.exists():
                errors.append(f"目录不存在: {directory}")
            elif not dir_path.is_dir():
                errors.append(f"路径不是目录: {directory}")
            elif not os.access(dir_path, os.R_OK):
                errors.append(f"没有读取权限: {directory}")
        
        return errors
    
    async def get_supported_file_types(self) -> List[str]:
        """获取支持的文件类型"""
        return [
            ".txt", ".doc", ".docx", ".pdf", ".xls", ".xlsx",
            ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff",
            ".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv",
            ".mp3", ".wav", ".flac", ".aac", ".ogg",
            ".zip", ".rar", ".7z", ".tar", ".gz"
        ]
    
    async def set_scan_priority(self, task_id: str, priority: ScanPriority) -> bool:
        """设置扫描优先级"""
        if task_id in self._scan_tasks:
            # 发布优先级变更事件
            event = create_business_event(
                BusinessEventType.SCAN_PRIORITY_CHANGED,
                self.get_service_name(),
                {
                    "task_id": task_id,
                    "priority": priority.value
                }
            )
            self._event_bus.publish("SCAN_PRIORITY_CHANGED", event)
            return True
        return False

    async def _execute_scan(self, request: ScanRequest) -> ScanResult:
        """执行扫描任务的核心逻辑"""
        start_time = time.time()
        scanned_files = []
        total_size = 0
        failed_files = 0

        try:
            # 初始化进度
            progress = ProgressUpdate(
                task_id=request.task_id,
                progress=0.0,
                status_message="开始扫描...",
                processed_items=0,
                total_items=0
            )
            self._scan_progress[request.task_id] = progress

            # 收集所有要扫描的文件
            all_files = []
            for directory in request.directories:
                file_collection = await self._collect_files(directory, request.recursive)
                all_files.extend(file_collection.files)

            total_files = len(all_files)

            # 更新总数
            progress.total_items = total_files
            progress.status_message = f"找到 {total_files} 个文件，开始处理..."
            self._scan_progress[request.task_id] = progress

            # 批量处理文件
            batch_size = min(DEFAULT_BATCH_SIZE, max(1, total_files // 10))

            for i in range(0, total_files, batch_size):
                batch = all_files[i:i + batch_size]

                # 并行处理批次
                batch_results = await self._process_file_batch(batch, request)

                for result in batch_results:
                    if result.success and result.item:
                        scanned_files.append(result.item)
                        total_size += result.item.size
                    else:
                        failed_files += 1

                # 更新进度
                processed = min(i + batch_size, total_files)
                progress.processed_items = processed
                progress.progress = (processed / total_files) * 100.0
                progress.status_message = f"已处理 {processed}/{total_files} 个文件"
                self._scan_progress[request.task_id] = progress

                # 检查是否被取消
                if request.task_id not in self._scan_tasks:
                    break

            # 创建扫描结果
            end_time = time.time()
            result = ScanResult(
                task_id=request.task_id,
                status=ScanStatus.COMPLETED,
                start_time=start_time,
                end_time=end_time,
                total_files=len(scanned_files),
                processed_files=len(scanned_files),
                failed_files=failed_files,
                total_size=total_size,
                scanned_directories=request.directories,
                files=scanned_files
            )

            # 保存结果
            self._scan_results[request.task_id] = result

            # 发布完成事件
            event = create_business_event(
                BusinessEventType.SCAN_COMPLETED,
                self.get_service_name(),
                result.to_dict()
            )
            self._event_bus.publish("SCAN_COMPLETED", event)

            return result

        except Exception as e:
            # 创建失败结果
            end_time = time.time()
            result = ScanResult(
                task_id=request.task_id,
                status=ScanStatus.FAILED,
                start_time=start_time,
                end_time=end_time,
                total_files=0,
                processed_files=len(scanned_files),
                failed_files=failed_files + 1,
                error_message=str(e)
            )

            self._scan_results[request.task_id] = result
            raise

    async def _collect_files(self, directory: str, recursive: bool) -> FileCollection:
        """
        收集目录中的文件

        Args:
            directory: 目录路径
            recursive: 是否递归扫描

        Returns:
            FileCollection: 文件集合DTO
        """
        try:
            return FileCollection.from_directory(directory, recursive)
        except Exception as e:
            # 记录错误但继续处理
            error_info = ErrorInfo(
                code="DIRECTORY_SCAN_FAILED",
                message=f"目录扫描失败: {directory}",
                details=str(e)
            )

            event = create_business_event(
                BusinessEventType.SCAN_ERROR,
                self.get_service_name(),
                error_info.to_dict()
            )
            self._event_bus.publish("SCAN_ERROR", event)

            # 返回空的文件集合
            return FileCollection(
                files=[],
                total_count=0,
                total_size=0,
                source_directory=directory,
                recursive=recursive
            )

    async def _process_file_batch(self, file_paths: List[str], request: ScanRequest) -> List[tuple]:
        """并行处理文件批次"""
        loop = asyncio.get_event_loop()

        # 使用线程池并行处理
        tasks = []
        for file_path in file_paths:
            task = loop.run_in_executor(
                self._executor,
                self._process_single_file,
                file_path,
                request
            )
            tasks.append(task)

        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                # 创建错误结果
                error_result = ProcessingResult.error_result(str(result))
                processed_results.append(error_result)
            else:
                processed_results.append(result)

        return processed_results

    def _process_single_file(self, file_path: str, request: ScanRequest) -> ProcessingResult:
        """
        处理单个文件

        Args:
            file_path: 文件路径
            request: 扫描请求

        Returns:
            ProcessingResult: 处理结果DTO
        """
        start_time = time.time()
        try:
            path = Path(file_path)
            stat = path.stat()

            # 创建文件信息
            file_info = FileInfo(
                path=str(path),
                name=path.name,
                size=stat.st_size,
                modified_time=stat.st_mtime,
                extension=path.suffix.lower(),
                is_directory=False
            )

            processing_time = time.time() - start_time
            return ProcessingResult.success_result(
                item=file_info,
                processing_time=processing_time,
                metadata={"file_size": stat.st_size, "extension": path.suffix.lower()}
            )

        except Exception as e:
            processing_time = time.time() - start_time
            return ProcessingResult.error_result(
                error_message=str(e),
                processing_time=processing_time,
                metadata={"file_path": file_path}
            )

    async def _monitor_scan_progress(self, task_id: str) -> AsyncGenerator[ProgressUpdate, None]:
        """监控扫描进度"""
        last_progress = 0.0

        while task_id in self._scan_tasks:
            current_progress = self._scan_progress.get(task_id)

            if current_progress and current_progress.progress > last_progress:
                yield current_progress
                last_progress = current_progress.progress

                # 如果完成，退出监控
                if current_progress.progress >= 100.0:
                    break

            # 等待一段时间再检查
            await asyncio.sleep(PROGRESS_CHECK_INTERVAL)

        # 确保最终进度被发送
        final_progress = self._scan_progress.get(task_id)
        if final_progress:
            yield final_progress

    # 实现IValidationService的抽象方法
    def validate_request(self, request) -> List[str]:
        """验证请求数据"""
        if hasattr(request, 'validate'):
            return request.validate()
        return []

    def validate_file_path(self, file_path: str) -> List[str]:
        """验证文件路径"""
        errors = []
        path = Path(file_path)

        if not path.exists():
            errors.append(f"文件不存在: {file_path}")
        elif not path.is_file():
            errors.append(f"路径不是文件: {file_path}")
        elif not os.access(path, os.R_OK):
            errors.append(f"没有读取权限: {file_path}")

        return errors

    def validate_directory_path(self, directory_path: str) -> List[str]:
        """验证目录路径"""
        errors = []
        path = Path(directory_path)

        if not path.exists():
            errors.append(f"目录不存在: {directory_path}")
        elif not path.is_dir():
            errors.append(f"路径不是目录: {directory_path}")
        elif not os.access(path, os.R_OK):
            errors.append(f"没有读取权限: {directory_path}")

        return errors

    def validate_file_operation(self, operation: str, file_paths: List[str]) -> List[str]:
        """验证文件操作"""
        errors = []

        if operation not in ["scan", "read", "index"]:
            errors.append(f"不支持的操作: {operation}")

        for file_path in file_paths:
            file_errors = self.validate_file_path(file_path)
            errors.extend(file_errors)

        return errors
